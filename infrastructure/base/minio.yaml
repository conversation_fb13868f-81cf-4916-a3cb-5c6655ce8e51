apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: minio
  namespace: default
  labels:
    app: minio
spec:
  serviceName: minio-service
  replicas: 1
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
    spec:
      containers:
        - name: minio
          image: minio/minio:latest
          command:
            - /bin/bash
            - -c
          args:
            - minio server /data --console-address :9001
          ports:
            - containerPort: 9000
              name: api
            - containerPort: 9001
              name: console
          env:
            - name: MINIO_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: accesskey
            - name: MINIO_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: secretkey
            - name: MINIO_ROOT_USER
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: accesskey
            - name: MINIO_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: secretkey
          volumeMounts:
            - name: minio-storage
              mountPath: /data
          # livenessProbe:
          #   httpGet:
          #     path: /minio/health/live
          #     port: 9000
          #   initialDelaySeconds: 30
          #   periodSeconds: 20
          # readinessProbe:
          #   httpGet:
          #     path: /minio/health/ready
          #     port: 9000
          #   initialDelaySeconds: 10
          #   periodSeconds: 5
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
  volumeClaimTemplates:
    - metadata:
        name: minio-storage
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 20Gi
---
apiVersion: v1
kind: Service
metadata:
  name: minio-api-service
  namespace: default
  labels:
    app: minio
spec:
  ports:
    - port: 9000
      targetPort: 9000
      name: api
  selector:
    app: minio
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: minio-console-service
  namespace: default
  labels:
    app: minio
spec:
  ports:
    - port: 9001
      targetPort: 9001
      name: console
  selector:
    app: minio
  type: ClusterIP
---
apiVersion: batch/v1
kind: Job
metadata:
  name: minio-bucket-creator
  namespace: default
spec:
  template:
    spec:
      restartPolicy: OnFailure
      initContainers:
        - name: wait-for-minio
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              until nc -z minio-api-service.default.svc.cluster.local 9000; do
                echo "Waiting for MinIO to be ready..."
                sleep 5
              done
              echo "MinIO is ready!"
      containers:
        - name: mc
          image: minio/mc:latest
          env:
            - name: MINIO_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: accesskey
            - name: MINIO_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: secretkey
          command:
            - /bin/sh
            - -c
          args:
            - |
              mc alias set myminio http://minio-api-service.default.svc.cluster.local:9000 $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
              
              # Create buckets for different environments
              mc mb myminio/dev-bucket --ignore-existing
              mc mb myminio/staging-bucket --ignore-existing
              mc mb myminio/prod-bucket --ignore-existing
              
              # Set bucket policies (public read for public assets)
              mc anonymous set download myminio/dev-bucket/public
              mc anonymous set download myminio/staging-bucket/public
              mc anonymous set download myminio/prod-bucket/public
              
              echo "Buckets created successfully!"
