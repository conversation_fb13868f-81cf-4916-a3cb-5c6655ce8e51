apiVersion: v1
kind: Namespace
metadata:
  name: portainer
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: portainer-sa-clusteradmin
  namespace: portainer
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: portainer-crb-clusteradmin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: portainer-sa-clusteradmin
  namespace: portainer
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: portainer-pvc
  namespace: portainer
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: portainer
  namespace: portainer
  labels:
    app: portainer
spec:
  replicas: 1
  selector:
    matchLabels:
      app: portainer
  template:
    metadata:
      labels:
        app: portainer
    spec:
      serviceAccountName: portainer-sa-clusteradmin
      containers:
      - name: portainer
        image: portainer/portainer-ce:2.19.4
        ports:
        - containerPort: 9000
          name: http
        - containerPort: 9443
          name: https
        - containerPort: 8000
          name: edge
        volumeMounts:
        - name: data
          mountPath: /data
        env:
        - name: PORTAINER_TUNNEL_PORT
          value: "8000"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 9000
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: portainer-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: portainer-service
  namespace: portainer
  labels:
    app: portainer
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 9000
    targetPort: 9000
  - name: https
    port: 9443
    targetPort: 9443
  - name: edge
    port: 8000
    targetPort: 8000
  selector:
    app: portainer
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: portainer-headers
  namespace: portainer
spec:
  headers:
    customRequestHeaders:
      X-Forwarded-Proto: "https"
      X-Forwarded-Port: "443"
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: portainer-stripprefix
  namespace: portainer
spec:
  stripPrefix:
    prefixes:
      - "/portainer"
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: portainer-ingress
  namespace: portainer
spec:
  entryPoints:
    - web
    - websecure
  routes:
  - match: Host(`portainer.local`) || Host(`portainer.localhost`)
    kind: Rule
    services:
    - name: portainer-service
      port: 9000
    middlewares:
    - name: portainer-headers
  tls: {}
